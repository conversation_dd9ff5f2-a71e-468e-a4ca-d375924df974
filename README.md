# Inkling Space

A creative idea management platform built with modern web technologies.

## Features

- 🔐 **Authentication** - Secure user authentication with Supabase
- 💡 **Idea Management** - Create, organize, and manage your creative ideas
- 🎨 **Modern UI** - Beautiful interface built with shadcn/ui components
- 📱 **Responsive Design** - Works seamlessly on desktop and mobile
- ⚡ **Fast Development** - Hot reload and instant preview with Vite

## Tech Stack

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Authentication**: Supabase Auth
- **Database**: Supabase PostgreSQL
- **Routing**: React Router
- **State Management**: TanStack Query

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Git

### Installation

1. Clone the repository:
```bash
git clone https://github.com/KainoaNewton/inkling-space.git
cd inkling-space
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Deployment

This project is configured for deployment on Vercel with proper SPA routing support.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
