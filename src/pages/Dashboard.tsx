import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { IdeaSearch } from "@/components/idea-search";
import { AccountDropdown } from "@/components/account-dropdown";
import { IdeaModal } from "@/components/idea-modal";
import type { User } from "@supabase/supabase-js";

// Mock data for ideas - replace with real data later
const mockIdeas = [
  {
    id: "1",
    title: "Skribble",
    description: "Sed lorem lorem ultricies lorem. Viverra pellentesque laoreet gravida eget. Nullam eget metus.",
    tags: ["web app", "mobile", "extension"],
    image: "📝"
  },
  {
    id: "2", 
    title: "Twitter",
    description: "Sed lorem lorem ultricies lorem. Viverra pellentesque laoreet gravida eget. Nullam eget metus.",
    tags: ["desktop", "mobile"],
    image: "🐦"
  }
];

const Dashboard = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [ideas] = useState(mockIdeas); // Will be replaced with real state management
  const [isIdeaModalOpen, setIsIdeaModalOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (session?.user) {
          setUser(session.user);
        } else {
          setUser(null);
          window.location.href = "/auth";
        }
        setLoading(false);
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        setUser(session.user);
      } else {
        window.location.href = "/auth";
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        toast({
          title: "Error signing out",
          description: error.message,
          variant: "destructive",
        });
      } else {
        window.location.href = "/auth";
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to auth
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold">Inkling Space</h1>
            
            <div className="flex items-center gap-4">
              <IdeaSearch ideas={ideas} />
              
              <Button onClick={() => setIsIdeaModalOpen(true)}>
                New Idea
              </Button>
              
              <AccountDropdown user={user} onSignOut={handleSignOut} />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {ideas.length === 0 ? (
          // Empty State
          <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
            <h2 className="text-xl font-medium mb-6">Create you're first idea</h2>
            <Button size="lg" onClick={() => setIsIdeaModalOpen(true)}>
              New Idea
            </Button>
          </div>
        ) : (
          // Ideas Grid
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {ideas.map((idea) => (
              <Card key={idea.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center text-lg">
                      {idea.image}
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg">{idea.title}</CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <CardDescription className="text-sm mb-4 leading-relaxed">
                    {idea.description}
                  </CardDescription>
                  <div className="flex flex-wrap gap-2">
                    {idea.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </main>
      
      <IdeaModal 
        open={isIdeaModalOpen} 
        onOpenChange={setIsIdeaModalOpen} 
      />
    </div>
  );
};

export default Dashboard;